from django.urls import path
from .views.dashboard_views import CampaignDashboardView
from .views import workflow_views

from .views.account_views import CampaignAccountsView, CampaignWhiteListView
from .views.simulation_views import simulation_dashboard, simulation_export, run_simulation
from .views.resource_manager_views import (
    ResourceManagerDashboardView, ResourceManagerDemoView, ResourceManagerStyleDemoView,
    PauseWorkflowView, ResumeWorkflowView, ResumeAllWorkflowsView, RemoveFromQueueView,
    ResourceManagerStatusView, OptimizeResourcesView, SetPriorityModeView
)
from .views.workflow_details_view import WorkflowDetailsView
from .views.cep_views import (
    CEPDashboardView, CEPWorkflowCreateView, CEPWorkflowDetailView,
    CEPWorkflowStartView, CEPWorkflowPauseView, CEPWorkflowResumeView,
    CEPWorkflowStopView, CEPWorkflowStatusAPIView
)

# Import all views from the views directory
from .views import (
    CampaignListView, CampaignDetailView, CampaignCreateView,
    CampaignUpdateView, CampaignDeleteView,
    AddLocationTargetView, AddLocationToTargetView, AddUsernameTargetView,
    RemoveLocationTargetView, RemoveUsernameTargetView,
    LaunchCampaignView, PauseCampaignView, ResumeCampaignView, StopCampaignView,
    ToggleFavoriteView, ExportCampaignView, AnalyzeCampaignView,
    DynamicTagListView, DynamicTagCreateView, DynamicTagUpdateView, DynamicTagDeleteView,
    TagGroupListView, TagGroupCreateView, TagGroupDetailView,
    TagGroupUpdateView, TagGroupDeleteView,
    AddTagToGroupView, RemoveTagFromGroupView, LocationSearchView, LocationCountriesView,
    AnalyzeAccountsAPIView, CheckTagExistsView
)

# Import tag views
from .views.tag_views import (
    CampaignTagListView, CampaignTagCreateView, CampaignTagUpdateView, CampaignTagDeleteView,
    TagSearchView, TagGroupAssignView, TagCategoriesView, TagGroupsView,
    CampaignTagToggleRequiredView, CampaignTagBulkActionView
)

# Import export views
from .views.export_views import (
    CampaignWhitelistExportView, CampaignAccountsExportView,
    CampaignDiscoveryExportView, LegacyCampaignExportView
)

app_name = 'campaigns'

urlpatterns = [
    # Campaign management
    path('', CampaignDashboardView.as_view(), name='dashboard'),
    path('list/', CampaignListView.as_view(), name='campaign_list'),
    path('create/', CampaignCreateView.as_view(), name='campaign_create'),
    path('<uuid:pk>/', CampaignDetailView.as_view(), name='campaign_detail'),
    path('<uuid:pk>/update/', CampaignUpdateView.as_view(), name='campaign_update'),
    path('<uuid:pk>/delete/', CampaignDeleteView.as_view(), name='campaign_confirm_delete'),

    # Target management
    path(
        '<uuid:pk>/add-location/',
        AddLocationTargetView.as_view(),
        name='add_location_target'
    ),
    path(
        '<uuid:pk>/add-location/select/',
        AddLocationToTargetView.as_view(),
        name='add_location_to_target'
    ),
    path(
        '<uuid:pk>/add-username/',
        AddUsernameTargetView.as_view(),
        name='add_username_target'
    ),
    path(
        '<uuid:campaign_pk>/remove-location/<uuid:target_pk>/',
        RemoveLocationTargetView.as_view(),
        name='remove_location_target'
    ),
    path(
        '<uuid:campaign_pk>/remove-username/<uuid:target_pk>/',
        RemoveUsernameTargetView.as_view(),
        name='remove_username_target'
    ),

    # Campaign actions
    path('<uuid:pk>/launch/', LaunchCampaignView.as_view(), name='launch_campaign'),
    path('<uuid:pk>/pause/', PauseCampaignView.as_view(), name='pause_campaign'),
    path('<uuid:pk>/resume/', ResumeCampaignView.as_view(), name='resume_campaign'),
    path('<uuid:pk>/stop/', StopCampaignView.as_view(), name='stop_campaign'),
    path('<uuid:pk>/toggle-favorite/', ToggleFavoriteView.as_view(), name='toggle_favorite'),

    # Legacy export (for backward compatibility)
    path('<uuid:pk>/export/', LegacyCampaignExportView.as_view(), name='export_campaign'),

    # New export endpoints
    path('<uuid:campaign_id>/export/whitelist/', CampaignWhitelistExportView.as_view(), name='export_whitelist'),
    path('<uuid:campaign_id>/export/accounts/', CampaignAccountsExportView.as_view(), name='export_accounts'),
    path('<uuid:campaign_id>/export/discovery/', CampaignDiscoveryExportView.as_view(), name='export_discovery'),

    # Campaign analysis
    path('<uuid:pk>/accounts/', CampaignAccountsView.as_view(), name='campaign_accounts'),
    path('<uuid:pk>/accounts/list/', CampaignAccountsView.as_view(), name='campaign_accounts_list'),
    path('<uuid:pk>/accounts/analyzed/', CampaignAccountsView.as_view(), name='campaign_accounts_analyzed'),
    path('<uuid:pk>/whitelist/', CampaignWhiteListView.as_view(), name='campaign_whitelist'),
    path('<uuid:pk>/analyze/', AnalyzeCampaignView.as_view(), name='analyze_campaign'),

    # Campaign tags
    path(
        '<uuid:campaign_id>/tags/',
        CampaignTagListView.as_view(),
        name='campaign_tags'
    ),
    path(
        '<uuid:campaign_id>/tags/create/',
        CampaignTagCreateView.as_view(),
        name='create_campaign_tag'
    ),
    path(
        'campaign-tags/<uuid:pk>/update/',
        CampaignTagUpdateView.as_view(),
        name='update_campaign_tag'
    ),
    path(
        'campaign-tags/<uuid:pk>/delete/',
        CampaignTagDeleteView.as_view(),
        name='delete_campaign_tag'
    ),

    # Dynamic Tag management
    path('tags/', DynamicTagListView.as_view(), name='dynamic_tag_list'),
    path('tags/create/', DynamicTagCreateView.as_view(), name='dynamic_tag_create'),
    path('tags/<uuid:pk>/update/', DynamicTagUpdateView.as_view(), name='dynamic_tag_update'),
    path('tags/<uuid:pk>/delete/', DynamicTagDeleteView.as_view(), name='dynamic_tag_delete'),

    # Tag Group management
    path('tag-groups/', TagGroupListView.as_view(), name='tag_group_list'),
    path('tag-groups/create/', TagGroupCreateView.as_view(), name='tag_group_create'),
    path('tag-groups/<uuid:pk>/', TagGroupDetailView.as_view(), name='tag_group_detail'),
    path('tag-groups/<uuid:pk>/update/', TagGroupUpdateView.as_view(), name='tag_group_update'),
    path('tag-groups/<uuid:pk>/delete/', TagGroupDeleteView.as_view(), name='tag_group_delete'),
    path('tag-groups/<uuid:pk>/add-tag/', AddTagToGroupView.as_view(), name='add_tag_to_group'),
    path(
        'tag-groups/<uuid:group_pk>/remove-tag/<uuid:tag_pk>/',
        RemoveTagFromGroupView.as_view(),
        name='remove_tag_from_group'
    ),

    # API endpoints
    path(
        'api/locations/search/',
        LocationSearchView.as_view(),
        name='api_location_search'
    ),
    path(
        'api/locations/countries/',
        LocationCountriesView.as_view(),
        name='api_location_countries'
    ),
    path(
        'api/accounts/analyze/',
        AnalyzeAccountsAPIView.as_view(),
        name='api_analyze_accounts'
    ),
    path(
        'api/tags/check-exists/',
        CheckTagExistsView.as_view(),
        name='api_check_tag_exists'
    ),

    # HTMX Tag endpoints
    path(
        '<uuid:campaign_id>/tags/search/',
        TagSearchView.as_view(),
        name='htmx_tag_search'
    ),
    path(
        '<uuid:campaign_id>/tags/assign-group/',
        TagGroupAssignView.as_view(),
        name='htmx_assign_tag_group'
    ),
    path(
        'api/tag-categories/',
        TagCategoriesView.as_view(),
        name='api_tag_categories'
    ),
    path(
        '<uuid:campaign_id>/tag-groups/',
        TagGroupsView.as_view(),
        name='htmx_tag_groups'
    ),

    # New HTMX endpoints for improved tag management
    path(
        'campaign-tags/<uuid:pk>/toggle-required/',
        CampaignTagToggleRequiredView.as_view(),
        name='htmx_toggle_tag_required'
    ),
    path(
        '<uuid:campaign_id>/tags/bulk-action/',
        CampaignTagBulkActionView.as_view(),
        name='htmx_bulk_tag_action'
    ),

    # Workflow API endpoints
    path(
        'api/campaigns/<uuid:campaign_id>/workflows/collection/',
        workflow_views.run_collection_workflow,
        name='api_run_collection_workflow'
    ),
    path(
        'api/campaigns/<uuid:campaign_id>/workflows/analysis/',
        workflow_views.run_analysis_workflow,
        name='api_run_analysis_workflow'
    ),
    path(
        'api/campaigns/<uuid:campaign_id>/workflows/engagement/',
        workflow_views.run_engagement_workflow,
        name='api_run_engagement_workflow'
    ),
    path(
        'api/campaigns/<uuid:campaign_id>/workflows/',
        workflow_views.list_campaign_workflows,
        name='api_list_campaign_workflows'
    ),
    path(
        'api/workflows/<uuid:workflow_execution_id>/',
        workflow_views.get_workflow_status,
        name='api_get_workflow_status'
    ),

    # Simulation views
    path(
        '<uuid:campaign_id>/simulation/',
        simulation_dashboard,
        name='simulation_dashboard'
    ),
    path(
        '<uuid:campaign_id>/simulation/export/',
        simulation_export,
        name='simulation_export'
    ),
    path(
        '<uuid:campaign_id>/simulation/run/',
        run_simulation,
        name='run_simulation'
    ),

    # Resource Manager views
    path(
        'resource-manager/',
        ResourceManagerDashboardView.as_view(),
        name='resource_manager_dashboard'
    ),
    path(
        'resource-manager/demo/',
        ResourceManagerDemoView.as_view(),
        name='resource_manager_demo'
    ),
    path(
        'resource-manager/style-demo/',
        ResourceManagerStyleDemoView.as_view(),
        name='resource_manager_style_demo'
    ),
    path(
        'api/resource-manager/status/',
        ResourceManagerStatusView.as_view(),
        name='resource-manager-status'
    ),
    path(
        'api/workflows/<str:workflow_id>/',
        WorkflowDetailsView.as_view(),
        name='workflow-details'
    ),
    path(
        'api/resource-manager/optimize/',
        OptimizeResourcesView.as_view(),
        name='optimize-resources'
    ),
    path(
        'api/resource-manager/pause-workflow/',
        PauseWorkflowView.as_view(),
        name='pause-workflow'
    ),
    path(
        'api/resource-manager/resume-workflow/',
        ResumeWorkflowView.as_view(),
        name='resume-workflow'
    ),
    path(
        'api/resource-manager/resume-all-workflows/',
        ResumeAllWorkflowsView.as_view(),
        name='resume-all-workflows'
    ),
    path(
        'api/resource-manager/remove-from-queue/',
        RemoveFromQueueView.as_view(),
        name='remove-from-queue'
    ),
    path(
        'api/resource-manager/set-priority-mode/',
        SetPriorityModeView.as_view(),
        name='set-priority-mode'
    ),

    # CEP Workflow views
    path(
        'cep/',
        CEPDashboardView.as_view(),
        name='cep_dashboard'
    ),
    path(
        'cep/create/',
        CEPWorkflowCreateView.as_view(),
        name='cep_create'
    ),
    path(
        'cep/<uuid:pk>/',
        CEPWorkflowDetailView.as_view(),
        name='cep_detail'
    ),
    path(
        'cep/<uuid:pk>/start/',
        CEPWorkflowStartView.as_view(),
        name='cep_start'
    ),
    path(
        'cep/<uuid:pk>/pause/',
        CEPWorkflowPauseView.as_view(),
        name='cep_pause'
    ),
    path(
        'cep/<uuid:pk>/resume/',
        CEPWorkflowResumeView.as_view(),
        name='cep_resume'
    ),
    path(
        'cep/<uuid:pk>/stop/',
        CEPWorkflowStopView.as_view(),
        name='cep_stop'
    ),
    path(
        'api/cep/<uuid:pk>/status/',
        CEPWorkflowStatusAPIView.as_view(),
        name='api_cep_status'
    ),
]
